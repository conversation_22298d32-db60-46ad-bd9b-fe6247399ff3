import os
import asyncio
from dotenv import load_dotenv
from app.core.vectordb import MilvusVectorDB

# Load environment variables
load_dotenv()

# Get Milvus connection parameters from environment
uri = os.getenv("VECTOR_DB_URI", "")
token = os.getenv("VECTOR_DB_TOKEN", "")

async def test_simple_json():
    print("Testing simple JSON metadata functionality...")
    
    # Create MilvusVectorDB instance
    db = MilvusVectorDB(uri=uri, token=token)
    
    try:
        # Connect to Milvus
        await db.connect()
        print("✅ Successfully connected to Milvus")
        
        # Create a test collection
        collection_name = "test_simple_json"
        dim = 3
        
        # Create collection with JSON schema
        await db.create_collection(collection_name, dim)
        print(f"✅ Created collection '{collection_name}' with JSON metadata field")
        
        # Insert test vectors with simple JSON metadata
        records = [
            {
                "vector": [1.0, 2.0, 3.0],
                "content": "Simple test content 1",
                "metadata": {
                    "category": "test",
                    "value": 100
                }
            },
            {
                "vector": [4.0, 5.0, 6.0],
                "content": "Simple test content 2",
                "metadata": {
                    "category": "demo",
                    "value": 200
                }
            }
        ]
        
        # Insert data
        result = await db.insert_vectors(collection_name, records)
        print(f"✅ Successfully inserted {result} vectors with JSON metadata")
        
        # Test simple search
        print("\n--- Testing simple search ---")
        search_vector = [1.0, 2.0, 3.0]
        results = await db.search_vectors(collection_name, search_vector, top_k=2)
        
        print(f"Found {len(results)} results:")
        for i, result in enumerate(results):
            print(f"  Result {i+1}: {result['content']}")
            print(f"    Metadata: {result['metadata']}")
            print(f"    Metadata type: {type(result['metadata'])}")
        
        print("✅ Simple JSON metadata test completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up
        try:
            if hasattr(db.client, 'drop_collection'):
                db.client.drop_collection(collection_name)
                print(f"🧹 Cleaned up test collection '{collection_name}'")
        except Exception as e:
            print(f"Warning: Could not clean up collection: {e}")

if __name__ == "__main__":
    asyncio.run(test_simple_json())
