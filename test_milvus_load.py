import os
import asyncio
from dotenv import load_dotenv
from app.core.vectordb import MilvusVectorDB

# Load environment variables
load_dotenv()

# Get Milvus connection parameters from environment
uri = os.getenv("VECTOR_DB_URI", "")
token = os.getenv("VECTOR_DB_TOKEN", "")

async def test_milvus_load():
    print("Testing Milvus load_collection...")
    
    # Create MilvusVectorDB instance
    db = MilvusVectorDB(uri=uri, token=token)
    
    # Connect to Milvus
    await db.connect()
    print("Successfully connected to Milvus")
    
    # Create a test collection
    collection_name = "test_load_collection"
    dim = 4
    
    # Create collection
    await db.create_collection(collection_name, dim)
    print(f"Test completed for collection '{collection_name}'")

if __name__ == "__main__":
    asyncio.run(test_milvus_load())
