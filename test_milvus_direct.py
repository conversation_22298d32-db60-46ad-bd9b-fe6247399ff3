import os
from dotenv import load_dotenv
from pymilvus import MilvusClient

# Load environment variables
load_dotenv()

# Get Milvus connection parameters from environment
uri = os.getenv("VECTOR_DB_URI", "")
token = os.getenv("VECTOR_DB_TOKEN", "")

def test_milvus_direct():
    print("Testing direct Milvus connection...")
    
    # Connect to Milvus
    client = MilvusClient(
        uri=uri,
        token=token if token and len(token.strip()) > 0 else None
    )
    print("Successfully connected to Milvus")
    
    # List collections
    collections = client.list_collections()
    print(f"Available collections: {collections}")
    
    # Create a test collection
    collection_name = "test_direct_collection"
    
    # Check if collection exists and drop it
    if collection_name in collections:
        print(f"Collection '{collection_name}' already exists, dropping it")
        client.drop_collection(collection_name)
    
    # Create collection
    client.create_collection(
        collection_name=collection_name,
        dimension=4  # Vector dimension
    )
    print(f"Created collection '{collection_name}'")
    
    # Check if load_collection method exists
    try:
        print("Trying to load collection...")
        client.load_collection(collection_name=collection_name)
        print(f"Successfully loaded collection '{collection_name}'")
    except AttributeError as e:
        print(f"load_collection method not available: {e}")
        print("This is expected in some versions of pymilvus")
    except Exception as e:
        print(f"Error loading collection: {e}")
    
    # Clean up
    client.drop_collection(collection_name)
    print(f"Dropped collection '{collection_name}'")
    print("Test completed")

if __name__ == "__main__":
    test_milvus_direct()
