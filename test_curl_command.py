import requests
import json

# 测试与原始curl命令相同的请求
def test_curl_command():
    url = "http://127.0.0.1:8000/api/v1/search"
    
    # 原始curl命令中的请求体
    payload = {
        "text": "新能源汽车是汽车产业转型升级的主要方向",
        "top_k": 5,
        "collection": "documents",
        "database": "rag_test_lkz"
    }
    
    # 设置正确的Content-Type
    headers = {"Content-Type": "application/json"}
    
    print("发送请求...")
    print(f"URL: {url}")
    print(f"请求头: {headers}")
    print(f"请求体: {json.dumps(payload, ensure_ascii=False)}")
    
    try:
        # 发送请求
        response = requests.post(url, json=payload, headers=headers)
        
        # 打印响应信息
        print("\n响应信息:")
        print(f"状态码: {response.status_code}")
        print(f"响应头: {response.headers}")
        print(f"响应体: {response.text}")
        
        if response.status_code == 200:
            print("\n✅ 请求成功!")
            # 解析并格式化JSON响应
            try:
                json_response = response.json()
                print("\n格式化响应:")
                print(json.dumps(json_response, indent=2, ensure_ascii=False))
            except:
                print("响应不是有效的JSON格式")
        else:
            print("\n❌ 请求失败!")
    except Exception as e:
        print(f"\n❌ 请求出错: {e}")

if __name__ == "__main__":
    test_curl_command()
