import asyncio
import os
from dotenv import load_dotenv
from app.core.vectordb import MilvusVectorDB

# Load environment variables
load_dotenv()

# Get Milvus connection parameters from environment
uri = os.getenv("VECTOR_DB_URI", "")
token = os.getenv("VECTOR_DB_TOKEN", "")

async def test_app_milvus():
    print("Testing application Milvus integration...")
    
    # Create MilvusVectorDB instance
    db = MilvusVectorDB(uri=uri, token=token)
    
    # Connect to Milvus
    await db.connect()
    print("Successfully connected to Milvus")
    
    # Create a test collection
    collection_name = "test_app_collection"
    dim = 4
    
    # Create collection
    await db.create_collection(collection_name, dim)
    print(f"Created collection '{collection_name}'")
    
    # Insert test vectors
    records = [
        {
            "vector": [1.0, 2.0, 3.0, 4.0],
            "content": "Test content 1",
            "metadata": {"test": "metadata1"}
        },
        {
            "vector": [5.0, 6.0, 7.0, 8.0],
            "content": "Test content 2",
            "metadata": {"test": "metadata2"}
        }
    ]
    
    try:
        result = await db.insert_vectors(collection_name, records)
        print(f"Successfully inserted {result} vectors")
    except Exception as e:
        print(f"Error inserting vectors: {e}")
    
    # Search vectors
    try:
        results = await db.search_vectors(collection_name, [1.0, 2.0, 3.0, 4.0], 2)
        print(f"Search results: {results}")
    except Exception as e:
        print(f"Error searching vectors: {e}")
    
    print("Test completed")

if __name__ == "__main__":
    asyncio.run(test_app_milvus())
