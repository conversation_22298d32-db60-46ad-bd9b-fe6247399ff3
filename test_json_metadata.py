import os
import asyncio
from dotenv import load_dotenv
from app.core.vectordb import MilvusVectorDB

# Load environment variables
load_dotenv()

# Get Milvus connection parameters from environment
uri = os.getenv("VECTOR_DB_URI", "")
token = os.getenv("VECTOR_DB_TOKEN", "")

async def test_json_metadata():
    print("Testing JSON metadata functionality...")
    
    # Create MilvusVectorDB instance
    db = MilvusVectorDB(uri=uri, token=token)
    
    # Connect to Milvus
    await db.connect()
    print("Successfully connected to Milvus")
    
    # Create a test collection
    collection_name = "test_json_metadata"
    dim = 4
    
    try:
        # Create collection with JSON schema
        await db.create_collection(collection_name, dim)
        print(f"Created collection '{collection_name}' with JSON metadata field")
        
        # Insert test vectors with JSON metadata
        records = [
            {
                "vector": [1.0, 2.0, 3.0, 4.0],
                "content": "Test content 1",
                "metadata": {
                    "product_info": {"category": "electronics", "brand": "BrandA"},
                    "price": 99.99,
                    "in_stock": True,
                    "tags": ["summer_sale", "clearance"]
                }
            },
            {
                "vector": [5.0, 6.0, 7.0, 8.0],
                "content": "Test content 2",
                "metadata": {
                    "product_info": {"category": "books", "brand": "BrandB"},
                    "price": 29.99,
                    "in_stock": False,
                    "tags": ["bestseller"]
                }
            },
            {
                "vector": [9.0, 10.0, 11.0, 12.0],
                "content": "Test content 3",
                "metadata": {
                    "product_info": {"category": "electronics", "brand": "BrandC"},
                    "price": 199.99,
                    "in_stock": True,
                    "tags": ["new_arrival"]
                }
            }
        ]
        
        # Insert data
        result = await db.insert_vectors(collection_name, records)
        print(f"Successfully inserted {result} vectors with JSON metadata")
        
        # Test search without filter
        print("\n--- Testing search without filter ---")
        search_vector = [1.0, 2.0, 3.0, 4.0]
        results = await db.search_vectors(collection_name, search_vector, top_k=3)
        
        print(f"Found {len(results)} results:")
        for i, result in enumerate(results):
            print(f"Result {i+1}:")
            print(f"  Content: {result['content']}")
            print(f"  Distance: {result['distance']}")
            print(f"  Metadata: {result['metadata']}")
            print(f"  Metadata type: {type(result['metadata'])}")
            print()
        
        # Test search with JSON filter - electronics category
        print("\n--- Testing search with JSON filter (electronics) ---")
        filter_expr = 'metadata["product_info"]["category"] == "electronics"'
        results = await db.search_vectors(collection_name, search_vector, top_k=3, filter_expr=filter_expr)
        
        print(f"Found {len(results)} electronics results:")
        for i, result in enumerate(results):
            print(f"Result {i+1}:")
            print(f"  Content: {result['content']}")
            print(f"  Distance: {result['distance']}")
            print(f"  Category: {result['metadata']['product_info']['category']}")
            print(f"  Brand: {result['metadata']['product_info']['brand']}")
            print(f"  Price: {result['metadata']['price']}")
            print()
        
        # Test search with JSON filter - price range
        print("\n--- Testing search with JSON filter (price < 100) ---")
        filter_expr = 'metadata["price"] < 100'
        results = await db.search_vectors(collection_name, search_vector, top_k=3, filter_expr=filter_expr)
        
        print(f"Found {len(results)} results with price < 100:")
        for i, result in enumerate(results):
            print(f"Result {i+1}:")
            print(f"  Content: {result['content']}")
            print(f"  Distance: {result['distance']}")
            print(f"  Price: {result['metadata']['price']}")
            print(f"  In stock: {result['metadata']['in_stock']}")
            print()
        
        # Test search with JSON filter - in stock items
        print("\n--- Testing search with JSON filter (in_stock = true) ---")
        filter_expr = 'metadata["in_stock"] == true'
        results = await db.search_vectors(collection_name, search_vector, top_k=3, filter_expr=filter_expr)
        
        print(f"Found {len(results)} in-stock results:")
        for i, result in enumerate(results):
            print(f"Result {i+1}:")
            print(f"  Content: {result['content']}")
            print(f"  Distance: {result['distance']}")
            print(f"  In stock: {result['metadata']['in_stock']}")
            print(f"  Brand: {result['metadata']['product_info']['brand']}")
            print()
            
        print("✅ JSON metadata test completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up - drop the test collection
        try:
            if hasattr(db.client, 'drop_collection'):
                db.client.drop_collection(collection_name)
                print(f"Cleaned up test collection '{collection_name}'")
        except Exception as e:
            print(f"Warning: Could not clean up collection: {e}")

if __name__ == "__main__":
    asyncio.run(test_json_metadata())
