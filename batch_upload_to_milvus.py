import asyncio
import requests
import pandas as pd
from pathlib import Path
import time

# 服务地址
API_URL = "http://copilot.csvw.com/rag_service/api/v1/upload_texts"

# 批量大小
BATCH_SIZE = 100

async def batch_upload_to_milvus():
    print("开始处理Excel文件并批量上传到Milvus向量库...")

    # 数据库和集合配置
    database = "SVWServiceTest"
    collection = "FaqRewriteQuestionIndexV1"

    # 查找data目录下的所有Excel文件
    data_dir = Path("app/data")
    if not data_dir.exists():
        print(f"目录 {data_dir} 不存在，正在创建...")
        data_dir.mkdir(exist_ok=True)
        print(f"请将Excel文件放入 {data_dir} 目录后重新运行脚本")
        return

    excel_files = list(data_dir.glob("*.xlsx")) + list(data_dir.glob("*.xls"))

    if not excel_files:
        print(f"在 {data_dir} 目录下未找到Excel文件")
        return

    # 使用找到的第一个Excel文件
    excel_file = excel_files[0]
    print(f"使用Excel文件: {excel_file}")

    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file)
        print(f"成功读取Excel文件，共 {len(df)} 行数据")

        # 检查是否有问题和答案列
        question_col = None
        answer_col = None

        # 查找问题和答案列
        for col in df.columns:
            col_lower = str(col).lower()
            if "question" in col_lower or "问题" in col_lower:
                question_col = col
            elif "answer" in col_lower or "答案" in col_lower:
                answer_col = col

        # 如果没有找到，尝试使用前两列
        if question_col is None and len(df.columns) >= 1:
            question_col = df.columns[0]
        if answer_col is None and len(df.columns) >= 2:
            answer_col = df.columns[1]

        if question_col is None or answer_col is None:
            print(f"无法识别问题和答案列，请确保Excel文件包含这些列")
            print(f"可用列: {list(df.columns)}")
            return

        print(f"使用列 '{question_col}' 作为问题列")
        print(f"使用列 '{answer_col}' 作为答案列")

        # 处理数据，批量上传
        total_rows = len(df)
        success_count = 0
        error_count = 0
        batch_count = 0

        # 创建批次
        batches = []
        current_batch = []
        batch_metadata = []

        for index, row in df.iterrows():
            question = str(row[question_col]).strip()
            answer = str(row[answer_col]).strip()

            # 跳过空问题
            if not question or question.lower() == "nan":
                print(f"\n跳过第 {index+1} 行: 问题为空")
                continue

            # 如果答案为空，设置默认值
            if not answer or answer.lower() == "nan":
                answer = "暂无答案"

            # 准备基本元数据
            metadata = {
                "question": question,
                "answer": answer,
                "source": f"Excel导入: {excel_file.name}",
                "row_index": int(index),
                "source_type": "excel_import"
            }

            # 处理所有其他列作为动态字段
            # 跳过问题和答案列，以及已经处理过的列
            processed_cols = [question_col, answer_col]
            extra_fields = {}

            for col in df.columns:
                if col in processed_cols:
                    continue

                # 获取列值
                value = row[col]

                # 跳过空值
                if pd.isna(value):
                    continue

                # 处理不同类型的值
                if isinstance(value, (int, float)) and not pd.isna(value):
                    # 对于数值类型，保持原始类型
                    field_value = value
                else:
                    # 对于其他类型，转换为字符串
                    field_value = str(value).strip()

                    # 如果是空字符串，跳过
                    if not field_value:
                        continue

                # 将列名转换为合适的字段名（去除空格，转为小写）
                field_name = str(col).strip().lower().replace(" ", "_")

                # 添加到元数据和额外字段中
                metadata[field_name] = field_value
                extra_fields[field_name] = field_value

            # 添加到当前批次
            current_batch.append(question)
            batch_metadata.append((metadata, extra_fields))

            # 如果当前批次已满或者是最后一行，则发送请求
            if len(current_batch) >= BATCH_SIZE or index == total_rows - 1:
                batches.append((current_batch, batch_metadata))
                current_batch = []
                batch_metadata = []
                batch_count += 1

        print(f"\n共创建 {batch_count} 个批次，每批最多 {BATCH_SIZE} 条记录")

        # 发送批次请求
        for batch_index, (texts, metadata_list) in enumerate(batches):
            print(f"\n处理批次 {batch_index+1}/{len(batches)}")
            print(f"批次大小: {len(texts)} 条记录")

            # 准备请求数据
            request_data = {
                "texts": texts,
                "database": database,
                "collection": collection,
                "encrypt": False,  # 不加密内容
                # "embedding_type": "azure-openai",  # 使用Azure OpenAI生成向量
                "metadata": {}  # 批量上传时，元数据在每条记录中单独指定
            }

            # 添加第一条记录的额外字段作为请求的额外字段
            # 注意：API会将这些字段应用到所有记录
            if metadata_list and metadata_list[0] and metadata_list[0][1]:
                for field_name, field_value in metadata_list[0][1].items():
                    request_data[field_name] = field_value

            # 发送请求
            try:
                start_time = time.time()
                print(f"发送批量上传请求...")
                response = requests.post(API_URL, json=request_data)
                end_time = time.time()

                # 打印响应
                print(f"状态码: {response.status_code}")
                print(f"请求耗时: {end_time - start_time:.2f} 秒")
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"成功上传批次")
                    print(f"文档ID: {result.get('doc_id', '')}")
                    print(f"上传文本数: {result.get('text_count', 0)}")
                    success_count += len(texts)
                else:
                    print(f"错误响应: {response.text}")
                    error_count += len(texts)
            except Exception as e:
                print(f"请求出错: {e}")
                error_count += len(texts)

            # 显示总体进度
            progress = (batch_index + 1) / len(batches) * 100
            print(f"总体进度: {progress:.1f}%")

        # 打印最终统计信息
        print(f"\n所有数据处理完成!")
        print(f"成功上传: {success_count} 条")
        print(f"失败数量: {error_count} 条")
        print(f"总批次数: {batch_count}")

    except Exception as e:
        print(f"处理Excel文件时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(batch_upload_to_milvus())
