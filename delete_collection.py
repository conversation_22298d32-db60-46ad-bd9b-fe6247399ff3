import asyncio
import os
from dotenv import load_dotenv
from app.core.vectordb import MilvusVectorDB

# 加载环境变量
load_dotenv()

# 获取Milvus连接参数
uri = os.getenv("VECTOR_DB_URI", "")
token = os.getenv("VECTOR_DB_TOKEN", "")

async def delete_collection():
    print("开始删除集合...")
    
    # 要删除的数据库和集合
    database = "rag_test_lkz"
    collection = "test_texts"
    
    # 创建MilvusVectorDB实例
    db = MilvusVectorDB(uri=uri, token=token, database=database)
    
    # 连接到Milvus
    try:
        await db.connect()
        print(f"成功连接到Milvus数据库: {database}")
        
        # 获取所有集合
        collections = db.collections_cache
        print(f"当前数据库中的集合: {collections}")
        
        # 检查集合是否存在
        if collection in collections:
            # 删除集合
            try:
                db.client.drop_collection(collection)
                print(f"成功删除集合: {collection}")
            except Exception as e:
                print(f"删除集合时出错: {e}")
        else:
            print(f"集合 {collection} 不存在")
            
    except Exception as e:
        print(f"连接到Milvus时出错: {e}")
    
    print("操作完成")

if __name__ == "__main__":
    asyncio.run(delete_collection())
