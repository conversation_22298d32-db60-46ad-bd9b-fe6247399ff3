import time
import asyncio
import sys
import os
from contextlib import asynccontextmanager
from dotenv import load_dotenv

# 显式加载.env文件
print("\n正在加载.env文件...")
load_dotenv()

# 检查.env文件是否存在
env_file_path = ".env"
if os.path.exists(env_file_path):
    print(f"\n.env文件存在: {os.path.abspath(env_file_path)}")
    try:
        with open(env_file_path, "r", encoding="utf-8") as f:
            env_content = f.read()
            print(f".env文件内容长度: {len(env_content)} 字节")
            # 打印文件内容（隐藏密码）
            for line in env_content.splitlines():
                if line.strip() and not line.strip().startswith("#"):
                    if "PASSWORD" in line:
                        key, value = line.split("=", 1)
                        print(f"  {key}={'*' * len(value)}")
                    else:
                        print(f"  {line}")
    except Exception as e:
        print(f"读取.env文件失败: {e}")
else:
    print(f"\n警告: .env文件不存在于当前目录: {os.getcwd()}")

# 打印环境变量信息以便调试
print("\n环境变量信息:")
for key, value in os.environ.items():
    if key.startswith("MODEL_") or key.startswith("VECTOR_DB_") or key.startswith("AZURE_OPENAI_"):
        # 如果是密码或API密钥，不显示实际值
        if "PASSWORD" in key or "API_KEY" in key:
            print(f"  {key} = {'*' * len(value)}")
        else:
            print(f"  {key} = {value}")
print("")

# 尝试导入必要的库
try:
    from fastapi import FastAPI
    from fastapi.middleware.cors import CORSMiddleware
    from app.routes import router
    from app.dependencies import get_embed_model_sync, get_vector_db_sync
except ImportError as e:
    print(f"\n\n错误: 无法导入必要的库: {e}")
    print("请安装缺失的库，或检查是否有DLL加载问题。")
    print("建议安装以下库：")
    print("pip install fastapi uvicorn pymupdf python-docx pdfplumber PyPDF2")
    sys.exit(1)

# 定义生命周期管理函数
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时执行
    print("\n" + "=" * 50)
    print("\u670d务正在启动，预加载模型和数据库...")
    start_time = time.time()

    # 预热数据库连接池
    try:
        print("预热数据库连接池...")
        pool_start = time.time()

        # 获取向量数据库配置
        from app.dependencies import get_vectordb_config_sync
        cfg = get_vectordb_config_sync()

        # 预热连接池
        from app.core.vectordb import MilvusVectorDB
        # 预热默认数据库连接
        await MilvusVectorDB.get_connection(
            uri=cfg.uri,
            token=cfg.token if cfg.token and len(cfg.token.strip()) > 0 else None,
            database=None
        )

        # 如果有常用的特定数据库，也可以预热
        # 例如预热 "SVWServiceTest" 数据库
        await MilvusVectorDB.get_connection(
            uri=cfg.uri,
            token=cfg.token if cfg.token and len(cfg.token.strip()) > 0 else None,
            database="SVWServiceTest"
        )

        pool_time = time.time() - pool_start
        print(f"数据库连接池预热完成，耗时: {pool_time:.2f}秒")
    except Exception as e:
        print(f"数据库连接池预热失败: {e}")
        print("将在首次请求时建立连接")

    # 异步预加载模型和数据库
    tasks = [
        get_embed_model_sync(),
        get_vector_db_sync()
    ]

    # 并行执行所有预加载任务
    await asyncio.gather(*tasks)

    # 计算总耗时
    total_time = time.time() - start_time
    print(f"\u6240有组件预加载完成，总耗时: {total_time:.2f} 秒")
    print("\u670d务已准备就绪，可以接受请求")
    print("=" * 50 + "\n")

    yield

    # 关闭时执行
    print("\n\u670d务正在关闭...")

# 创建FastAPI应用
app = FastAPI(title="RAG Vector Service", lifespan=lifespan)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加请求体解析错误处理
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from fastapi import Request
import traceback

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    print(f"\n[错误] 请求体解析错误: {exc}")
    print(f"[错误] 请求URL: {request.url}")
    print(f"[错误] 请求方法: {request.method}")
    print(f"[错误] 请求头: {dict(request.headers)}")
    print(f"[错误] Content-Type: {request.headers.get('content-type')}")
    print(f"[错误] Content-Length: {request.headers.get('content-length')}")

    # 尝试获取原始请求体
    body = b""
    try:
        body = await request.body()
        body_str = body.decode('utf-8', errors='replace')
        print(f"[错误] 原始请求体: {body_str}")
        print(f"[错误] 请求体长度: {len(body)} 字节")
        print(f"[错误] 请求体类型: {type(body)}")

        # 尝试解析JSON
        try:
            import json
            json_data = json.loads(body_str)
            print(f"[错误] JSON解析结果: {json_data}")
            print(f"[错误] JSON类型: {type(json_data)}")
        except json.JSONDecodeError as json_err:
            print(f"[错误] JSON解析失败: {json_err}")
            print(f"[错误] JSON错误位置: 第 {json_err.lineno} 行, 第 {json_err.colno} 列")
            print(f"[错误] JSON错误字符: '{json_err.doc[max(0, json_err.pos-20):json_err.pos]}[HERE>{json_err.doc[json_err.pos:min(len(json_err.doc), json_err.pos+20)]}]'")
    except Exception as e:
        print(f"[错误] 无法读取请求体: {e}")
        print(f"[错误] 异常类型: {type(e)}")

    # 打印详细的错误堆栈
    print(f"[错误] 详细错误信息: {traceback.format_exc()}")

    # 打印验证错误详情
    for error in exc.errors():
        print(f"[错误] 验证错误: {error}")
        print(f"[错误] 错误位置: {error.get('loc', '未知')}")
        print(f"[错误] 错误类型: {error.get('type', '未知')}")
        print(f"[错误] 错误消息: {error.get('msg', '未知')}")

    return JSONResponse(
        status_code=422,
        content={
            "detail": "请求体解析错误，请检查请求格式",
            "errors": exc.errors(),
            "body": body_str if body else None,
            "content_type": request.headers.get('content-type')
        },
    )

# 集成路由
app.include_router(router)

# 添加全局异常处理
from fastapi import HTTPException

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    print(f"\n[错误] 全局异常: {exc}")
    print(f"[错误] 异常类型: {type(exc).__name__}")
    print(f"[错误] 请求URL: {request.url}")
    print(f"[错误] 请求方法: {request.method}")
    print(f"[错误] 请求头: {dict(request.headers)}")
    print(f"[错误] Content-Type: {request.headers.get('content-type')}")

    # 尝试获取原始请求体
    body = b""
    body_str = ""
    try:
        body = await request.body()
        body_str = body.decode('utf-8', errors='replace')
        print(f"[错误] 原始请求体: {body_str}")
        print(f"[错误] 请求体长度: {len(body)} 字节")

        # 尝试解析JSON
        if request.headers.get('content-type') == 'application/json':
            try:
                import json
                json_data = json.loads(body_str)
                print(f"[错误] JSON解析结果: {json_data}")
                print(f"[错误] JSON类型: {type(json_data)}")
            except json.JSONDecodeError as json_err:
                print(f"[错误] JSON解析失败: {json_err}")
                print(f"[错误] JSON错误位置: 第 {json_err.lineno} 行, 第 {json_err.colno} 列")
                print(f"[错误] JSON错误字符: '{json_err.doc[max(0, json_err.pos-20):json_err.pos]}[HERE>{json_err.doc[json_err.pos:min(len(json_err.doc), json_err.pos+20)]}]'")
    except Exception as e:
        print(f"[错误] 无法读取请求体: {e}")
        print(f"[错误] 异常类型: {type(e)}")

    # 打印详细的错误堆栈
    print(f"[错误] 详细错误信息:\n{traceback.format_exc()}")

    return JSONResponse(
        status_code=500,
        content={
            "detail": f"服务器内部错误: {str(exc)}",
            "type": str(type(exc).__name__),
            "traceback": traceback.format_exc().split('\n'),
            "body": body_str if body else None,
            "content_type": request.headers.get('content-type')
        },
    )

@app.get("/health")
async def health_check():
    return {"status": "ok"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        workers=4,
        timeout_keep_alive=300
    )