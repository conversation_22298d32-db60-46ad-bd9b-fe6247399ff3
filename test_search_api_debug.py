import requests
import json
import sys
import time

# 测试搜索API的不同请求格式
def test_search_api_debug():
    url = "http://127.0.0.1:8000/api/v1/search"
    
    # 测试用例1: 标准请求 (使用双引号)
    payload1 = {
        "text": "新能源汽车是汽车产业转型升级的主要方向",
        "top_k": 5,
        "collection": "documents_V1",
        "database": "rag_test_lkz"
    }
    
    # 测试用例2: 使用数字类型的top_k
    payload2 = {
        "text": "新能源汽车是汽车产业转型升级的主要方向",
        "top_k": 5,  # 数字类型
        "collection": "documents_V1",
        "database": "rag_test_lkz"
    }
    
    # 测试用例3: 使用字符串类型的top_k
    payload3 = {
        "text": "新能源汽车是汽车产业转型升级的主要方向",
        "top_k": "5",  # 字符串类型
        "collection": "documents_V1",
        "database": "rag_test_lkz"
    }
    
    # 测试用例4: 使用原始curl命令中的格式
    payload4 = '''{
  "text": "新能源汽车是汽车产业转型升级的主要方向",
  "top_k": 5,
  "collection": "documents_V1",
  "database": "rag_test_lkz"
}'''
    
    # 测试用例5: 使用紧凑格式
    payload5 = '{"text":"新能源汽车是汽车产业转型升级的主要方向","top_k":5,"collection":"documents_V1","database":"rag_test_lkz"}'
    
    # 测试用例6: 使用带空格的格式
    payload6 = '{ "text" : "新能源汽车是汽车产业转型升级的主要方向", "top_k" : 5, "collection" : "documents_V1", "database" : "rag_test_lkz" }'
    
    # 测试所有用例
    test_cases = [
        ("标准请求(字典)", payload1, "application/json"),
        ("数字类型top_k(字典)", payload2, "application/json"),
        ("字符串类型top_k(字典)", payload3, "application/json"),
        ("原始curl格式(字符串)", payload4, "application/json"),
        ("紧凑JSON格式(字符串)", payload5, "application/json"),
        ("带空格JSON格式(字符串)", payload6, "application/json")
    ]
    
    for name, payload, content_type in test_cases:
        print(f"\n\n{'=' * 50}")
        print(f"测试用例: {name}")
        print(f"{'=' * 50}")
        
        try:
            headers = {"Content-Type": content_type}
            
            # 如果是字典，转换为JSON字符串
            if isinstance(payload, dict):
                payload_str = json.dumps(payload, ensure_ascii=False)
            else:
                payload_str = payload
                
            print(f"请求URL: {url}")
            print(f"请求头: {headers}")
            print(f"请求体类型: {type(payload)}")
            print(f"请求体内容: {payload}")
            print(f"发送的实际请求体: {payload_str}")
            print(f"实际请求体类型: {type(payload_str)}")
            print(f"实际请求体长度: {len(payload_str)} 字节")
            
            # 发送请求
            print("\n发送请求...")
            start_time = time.time()
            response = requests.post(url, data=payload_str, headers=headers)
            request_time = time.time() - start_time
            
            print(f"请求耗时: {request_time:.3f}秒")
            print(f"状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            
            # 尝试解析响应体为JSON
            try:
                response_json = response.json()
                print(f"响应体(JSON): {json.dumps(response_json, ensure_ascii=False, indent=2)}")
            except:
                print(f"响应体(文本): {response.text}")
            
            if response.status_code == 200:
                print("✅ 测试成功!")
            else:
                print("❌ 测试失败!")
        except Exception as e:
            print(f"❌ 测试出错: {e}")
            import traceback
            print(traceback.format_exc())

if __name__ == "__main__":
    test_search_api_debug()
