import os
from dotenv import load_dotenv
from pymilvus import MilvusClient

# Load environment variables
load_dotenv()

# Get Milvus connection parameters from environment
uri = os.getenv("VECTOR_DB_URI", "")
token = os.getenv("VECTOR_DB_TOKEN", "")

def test_milvus_connection():
    print("Testing Milvus connection...")

    # Connect to Milvus
    client = MilvusClient(
        uri=uri,
        token=token if token and len(token.strip()) > 0 else None
    )
    print("Successfully connected to Milvus")

    # Create a test collection
    collection_name = "test_collection"

    # Check if collection exists and drop it
    collections = client.list_collections()
    if collection_name in collections:
        print(f"Collection '{collection_name}' already exists, dropping it")
        client.drop_collection(collection_name)

    # Create collection with vector field
    client.create_collection(
        collection_name=collection_name,
        dimension=4,  # Vector dimension
        auto_id=True  # Auto-generate IDs
    )
    print(f"Created collection '{collection_name}'")

    # Insert data - without providing 'id' field
    entities = [
        {
            "vector": [1.0, 2.0, 3.0, 4.0],
            "content": "Test content 1",
            "collection_name": collection_name
        },
        {
            "vector": [5.0, 6.0, 7.0, 8.0],
            "content": "Test content 2",
            "collection_name": collection_name
        }
    ]

    try:
        insert_result = client.insert(
            collection_name=collection_name,
            data=entities
        )
        print(f"Successfully inserted {len(entities)} entities")
        print(f"Insert result: {insert_result}")
    except Exception as e:
        print(f"Error inserting data: {e}")

    # Clean up
    client.drop_collection(collection_name)
    print(f"Dropped collection '{collection_name}'")

if __name__ == "__main__":
    test_milvus_connection()
